#!/usr/bin/env python3
"""
Example usage of the enhanced test_classification.py script with table output.

This script demonstrates how to use the classification script and shows
the table output feature.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))

from test_classification import FileProcessor


def example_basic_usage():
    """Example of basic usage with the enhanced script."""
    
    print("🔍 Example 1: Basic Classification with Table Output")
    print("=" * 60)
    
    # Example folder path - adjust this to your actual folder
    folder_path = "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket"
    max_files = 2
    
    async def run_classification():
        # Initialize processor
        processor = FileProcessor("Augment_test/example_output", "Augment_test/example_logs")
        
        # Process files
        stats = await processor.process_files(
            folder_path=folder_path,
            max_files=max_files,
            run_number=100
        )
        
        return stats
    
    # Run the classification
    result = asyncio.run(run_classification())
    
    print(f"\n✅ Example 1 completed: {result['processed']}/{result['total_files']} files processed")
    return result


def example_multiple_categories():
    """Example showing classification of multiple document types."""
    
    print("\n🔍 Example 2: Multiple Document Categories")
    print("=" * 60)
    
    # Different document type folders
    test_folders = [
        "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol",
        "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice",
        "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket"
    ]
    
    async def run_multi_classification():
        processor = FileProcessor("Augment_test/example_output_multi", "Augment_test/example_logs")
        
        # Collect one file from each category
        all_files = []
        for folder in test_folders:
            if os.path.exists(folder):
                files = processor.get_supported_files(folder)
                if files:
                    all_files.append(files[0])  # Take first file from each category
        
        if not all_files:
            print("❌ No test files found")
            return None
        
        print(f"📁 Processing {len(all_files)} files from different categories")
        
        # Process files individually to simulate mixed batch
        processed_files = []
        for file_path in all_files:
            try:
                result = await processor.process_single_file(file_path, 101)
                if result[0]:  # success
                    processed_files.append({
                        "filename": result[2],
                        "classification": result[3],
                        "file_path": file_path
                    })
            except Exception as e:
                print(f"❌ Failed to process {file_path}: {e}")
        
        # Print the table
        processor.print_classification_table(processed_files, all_files)
        
        return {"processed": len(processed_files), "total": len(all_files)}
    
    result = asyncio.run(run_multi_classification())
    
    if result:
        print(f"\n✅ Example 2 completed: {result['processed']}/{result['total']} files processed")
    
    return result


def show_table_features():
    """Show the key features of the classification table."""
    
    print("\n📋 Table Features:")
    print("-" * 40)
    print("✓ Clickable PDF file links (file:// protocol)")
    print("✓ Page numbers for multi-page documents")
    print("✓ Classified document categories")
    print("✓ Both detailed and compact table views")
    print("✓ Easy verification of classification results")
    print("✓ Direct PDF access from terminal")
    
    print("\n💡 Usage Tips:")
    print("-" * 40)
    print("• Click on file:// links to open PDFs directly")
    print("• Use the compact view for quick overview")
    print("• Check page numbers for multi-page documents")
    print("• Verify classifications against expected categories")
    print("• Use the table to spot misclassifications quickly")


if __name__ == "__main__":
    print("🚀 Enhanced Classification Script Examples")
    print("=" * 80)
    
    # Create output directories
    os.makedirs("Augment_test/example_output", exist_ok=True)
    os.makedirs("Augment_test/example_output_multi", exist_ok=True)
    os.makedirs("Augment_test/example_logs", exist_ok=True)
    
    # Run examples
    try:
        result1 = example_basic_usage()
        result2 = example_multiple_categories()
        
        show_table_features()
        
        print("\n" + "=" * 80)
        print("🎉 All examples completed!")
        
        if result1:
            print(f"✅ Example 1: {result1['processed']} files processed")
        if result2:
            print(f"✅ Example 2: {result2['processed']} files processed")
            
        print("\n📋 The table output makes it easy to:")
        print("   • Verify classification accuracy")
        print("   • Open specific PDFs for review")
        print("   • Track processing results")
        print("   • Identify misclassifications quickly")
        
    except Exception as e:
        print(f"❌ Error running examples: {e}")
        print("💡 Make sure the input folders exist and contain PDF files")
