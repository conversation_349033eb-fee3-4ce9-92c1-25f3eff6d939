#!/usr/bin/env python3
"""
Test script to demonstrate the classification table functionality.

This script tests the enhanced test_classification.py with the new table output feature.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))

from test_classification import FileProcessor


async def test_classification_table():
    """Test the classification with table output."""
    
    # Test with a small subset of files
    test_folder = "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/6_scale_ticket"
    max_files = 3  # Process only 3 files for demo
    
    print("🧪 Testing Classification Table Demo")
    print(f"📁 Test folder: {test_folder}")
    print(f"📊 Max files: {max_files}")
    print("-" * 60)
    
    # Initialize processor
    processor = FileProcessor("Augment_test/output", "Augment_test/logs")
    
    try:
        # Process files
        stats = await processor.process_files(
            test_folder,
            max_files,
            run_number=999  # Use 999 to distinguish test runs
        )
        
        print("\n✅ Test completed successfully!")
        print(f"📈 Processed: {stats['processed']}/{stats['total_files']} files")
        print(f"⏱️  Duration: {stats['duration_seconds']:.2f} seconds")
        
        if stats['failed'] > 0:
            print(f"❌ Failed: {stats['failed']} files")
            
        return stats
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return None


async def test_with_different_categories():
    """Test with files from different categories to show variety in the table."""
    
    print("\n🧪 Testing with Multiple Categories")
    print("-" * 60)
    
    # Test folders with different document types
    test_folders = [
        "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/1_bol",
        "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/10_invoice", 
        "/home/<USER>/Desktop/test_ligistically/input_data_3_per_cat/2_pod"
    ]
    
    processor = FileProcessor("Augment_test/output_multi", "Augment_test/logs")
    
    all_files = []
    for folder in test_folders:
        if os.path.exists(folder):
            folder_files = processor.get_supported_files(folder)
            # Take first file from each category
            if folder_files:
                all_files.append(folder_files[0])
    
    if not all_files:
        print("❌ No test files found in the specified folders")
        return None
    
    print(f"📁 Processing {len(all_files)} files from different categories")
    
    # Process each file individually to simulate a mixed batch
    processed_files = []
    for i, file_path in enumerate(all_files):
        try:
            result = await processor.process_single_file(file_path, 998)
            if result[0]:  # success
                processed_files.append({
                    "filename": result[2],
                    "classification": result[3],
                    "file_path": file_path
                })
        except Exception as e:
            print(f"❌ Failed to process {file_path}: {e}")
    
    # Print the table manually
    processor.print_classification_table(processed_files, all_files)
    
    return {"processed": len(processed_files), "total": len(all_files)}


if __name__ == "__main__":
    print("🚀 Starting Classification Table Demo")
    print("=" * 80)
    
    # Run the first test
    result1 = asyncio.run(test_classification_table())
    
    # Run the second test with multiple categories
    result2 = asyncio.run(test_with_different_categories())
    
    print("\n" + "=" * 80)
    print("🎉 Demo completed!")
    
    if result1:
        print(f"✅ Test 1: {result1['processed']} files processed")
    if result2:
        print(f"✅ Test 2: {result2['processed']} files processed")
    
    print("\n📋 The table shows:")
    print("   • PDF file links (clickable in terminal)")
    print("   • Page numbers for each classified page")
    print("   • Classified document categories")
    print("   • Both detailed and compact views")
    print("\n💡 You can click on the file:// links to open PDFs directly!")
