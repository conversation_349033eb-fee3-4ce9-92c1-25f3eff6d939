{"classification_result": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 0.9746862649917603, "Height": 1.0, "Left": 0.013133395463228226, "Top": 0.0}, "Polygon": [{"X": 0.01372542418539524, "Y": 0.0}, {"X": 0.9878196716308594, "Y": 9.52283062360948e-06}, {"X": 0.9876441359519958, "Y": 1.0}, {"X": 0.013133395463228226, "Y": 0.999885618686676}]}, "Id": "8c1708b8-bcc1-432e-87e2-371f272e7627", "Relationships": [{"Type": "CHILD", "Ids": ["c1f73baf-de3b-4083-9d59-f11c200ce9fe", "4d504c39-6b31-4f16-b18e-627b744abd02", "19db9b59-666f-4b26-875a-c39ed1c7cafa", "4fdd9d71-bd94-4e73-8e6c-d33d418d3868", "ceede36a-f9f2-4568-996b-f769293f5976", "dc8af2ca-ce2d-43d0-8bd9-2093036af4e7", "6e98112d-15f0-48f9-8c24-2e533b1692ca", "7206dad2-5727-427f-b34b-4bf93b4b8ebc", "8d8b1935-e13b-4442-a651-4bce47e2707c", "3fb3e001-3df2-4539-9e6e-92425f0fd80a", "7e35547d-1889-4803-8754-2c66d9d003fc", "97d1fa28-9d69-4722-852c-6ff1398a7db3", "78711691-6086-4bf4-9b58-2db9cf2ccbed", "2b1f48e8-5f6c-4475-9da4-2381973ef2c1", "57179dc7-af83-4035-bd5a-621105269f3f", "eb6ab713-ea20-4c11-ad88-80aa5a663933", "38fd1398-b3db-460f-8265-c9a8b40a1517", "e3cfcc4a-e2ea-444e-8e84-3ba7f563f6b6", "15a0837b-d63c-4b7e-905e-02c1fa065590", "035b92d4-ad77-44c6-bf09-683454b879d2", "3aa342a0-9501-476b-87f9-f3fcc5443b1f", "0b98ac00-32d1-422c-9d94-70a9b3b3a80b", "46466636-0406-493c-bf1f-c93b6475f8ea", "e265b4d4-5a63-46ff-aa7a-f9a78cf1ca2f", "35b7a6c0-b3e5-4df8-842e-86be447ddefb", "8d1290e0-468d-4fd7-acda-513343150128", "dd2490bd-ce75-43d5-bd23-bd12b5b79b5d", "ce9859eb-9335-4450-bab5-f697c59c3260", "f02058a0-7a42-47a8-bda6-e602295fa31a", "43be3d81-224a-4779-8ad4-f57bedece575", "2812201e-4a23-467f-a681-a5b2a104043e", "43ad4153-4c89-4d47-abf3-6640a2a897db", "1be70b73-5eae-40f4-8598-34aaf392765e", "aa680c22-c2d1-441f-bacf-26d15921976a", "3b03a192-ba56-47e0-b38b-b6ea2de240f0", "a2bab5e9-a751-4608-9782-ed3dbf42eb34", "a808fa7c-3199-4863-8cef-844605a8729a", "827202d0-a379-45fd-9888-9486754bc127"]}]}, {"BlockType": "LINE", "Confidence": 99.91755676269531, "Text": "ELECTRONIC SCALE TICKET", "Geometry": {"BoundingBox": {"Width": 0.31597304344177246, "Height": 0.024880940094590187, "Left": 0.3993188440799713, "Top": 0.010396572761237621}, "Polygon": [{"X": 0.39932942390441895, "Y": 0.010396572761237621}, {"X": 0.7152918577194214, "Y": 0.010470840148627758}, {"X": 0.7152846455574036, "Y": 0.03527751564979553}, {"X": 0.3993188440799713, "Y": 0.03520360216498375}]}, "Id": "c1f73baf-de3b-4083-9d59-f11c200ce9fe", "Relationships": [{"Type": "CHILD", "Ids": ["87f52016-c4e0-4013-bbaf-b55fab773917", "06c8967c-aa65-4f8c-b28a-9ded93a928b8", "a5d094a9-14bd-48ed-9e57-f91964f4bf48"]}]}, {"BlockType": "LINE", "Confidence": 68.63221740722656, "Text": "EIL", "Geometry": {"BoundingBox": {"Width": 0.07379066944122314, "Height": 0.027249764651060104, "Left": 0.7669656872749329, "Top": 0.0038279935251921415}, "Polygon": [{"X": 0.7669730186462402, "Y": 0.0038279935251921415}, {"X": 0.840756356716156, "Y": 0.0038453585002571344}, {"X": 0.8407498598098755, "Y": 0.031077757477760315}, {"X": 0.7669656872749329, "Y": 0.031060483306646347}]}, "Id": "4d504c39-6b31-4f16-b18e-627b744abd02", "Relationships": [{"Type": "CHILD", "Ids": ["e5a5ea4d-1dcb-4a70-b556-263795997cf0"]}]}, {"BlockType": "LINE", "Confidence": 73.07328033447266, "Text": "NC", "Geometry": {"BoundingBox": {"Width": 0.06511225551366806, "Height": 0.030571557581424713, "Left": 0.7649180293083191, "Top": 0.03962692245841026}, "Polygon": [{"X": 0.7649262547492981, "Y": 0.03962692245841026}, {"X": 0.8300302624702454, "Y": 0.03964213654398918}, {"X": 0.8300228118896484, "Y": 0.07019847631454468}, {"X": 0.7649180293083191, "Y": 0.07018335163593292}]}, "Id": "19db9b59-666f-4b26-875a-c39ed1c7cafa", "Relationships": [{"Type": "CHILD", "Ids": ["7299c1a6-e736-41a7-8595-7ab26e72ab86"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "DATE:", "Geometry": {"BoundingBox": {"Width": 0.0560796782374382, "Height": 0.016097577288746834, "Left": 0.16496825218200684, "Top": 0.06779459118843079}, "Polygon": [{"X": 0.16497673094272614, "Y": 0.06779459118843079}, {"X": 0.22104793787002563, "Y": 0.0678076222538948}, {"X": 0.22103984653949738, "Y": 0.08389216661453247}, {"X": 0.16496825218200684, "Y": 0.08387918025255203}]}, "Id": "4fdd9d71-bd94-4e73-8e6c-d33d418d3868", "Relationships": [{"Type": "CHILD", "Ids": ["dec161ae-c112-4fff-8a81-e8de28e494b7"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "MAILING", "Geometry": {"BoundingBox": {"Width": 0.08374965935945511, "Height": 0.016218237578868866, "Left": 0.16687875986099243, "Top": 0.1219445988535881}, "Polygon": [{"X": 0.1668872833251953, "Y": 0.1219445988535881}, {"X": 0.25062841176986694, "Y": 0.12196385115385056}, {"X": 0.2506204843521118, "Y": 0.13816283643245697}, {"X": 0.16687875986099243, "Y": 0.1381436437368393}]}, "Id": "ceede36a-f9f2-4568-996b-f769293f5976", "Relationships": [{"Type": "CHILD", "Ids": ["e28267be-00a7-46de-9a42-f3b186bc858b"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "ADDRESS", "Geometry": {"BoundingBox": {"Width": 0.08829937130212784, "Height": 0.016302989795804024, "Left": 0.16692252457141876, "Top": 0.14857086539268494}, "Polygon": [{"X": 0.16693109273910522, "Y": 0.14857086539268494}, {"X": 0.2552219033241272, "Y": 0.1485910713672638}, {"X": 0.2552139461040497, "Y": 0.1648738533258438}, {"X": 0.16692252457141876, "Y": 0.16485372185707092}]}, "Id": "dc8af2ca-ce2d-43d0-8bd9-2093036af4e7", "Relationships": [{"Type": "CHILD", "Ids": ["c368cb48-9135-49af-bb44-8211462e561c"]}]}, {"BlockType": "LINE", "Confidence": 98.9308853149414, "Text": "DLA DISPOSITION SERVICES - COLUMBUS", "Geometry": {"BoundingBox": {"Width": 0.4137175977230072, "Height": 0.024917976930737495, "Left": 0.30360445380210876, "Top": 0.14137618243694305}, "Polygon": [{"X": 0.30361607670783997, "Y": 0.14137618243694305}, {"X": 0.717322051525116, "Y": 0.14147095382213593}, {"X": 0.7173148393630981, "Y": 0.1662941575050354}, {"X": 0.30360445380210876, "Y": 0.16619983315467834}]}, "Id": "6e98112d-15f0-48f9-8c24-2e533b1692ca", "Relationships": [{"Type": "CHILD", "Ids": ["e4bafa10-8b91-4cee-9f88-fba445efe28f", "626657d6-b51b-444b-8389-1ea83f2fe602", "f10f30f5-a463-43e2-b4db-c6ea8afebab8", "e3d29e27-819a-4750-ac4f-ffc255d5bdc0", "8597ddca-a0e5-43ab-8677-fad914edc080"]}]}, {"BlockType": "LINE", "Confidence": 99.4625244140625, "Text": "3990 E. BROAD ST, BLDG 30", "Geometry": {"BoundingBox": {"Width": 0.27718833088874817, "Height": 0.02507944218814373, "Left": 0.3036785423755646, "Top": 0.1669808030128479}, "Polygon": [{"X": 0.30369025468826294, "Y": 0.1669808030128479}, {"X": 0.5808668732643127, "Y": 0.16704398393630981}, {"X": 0.5808581113815308, "Y": 0.19206024706363678}, {"X": 0.3036785423755646, "Y": 0.19199737906455994}]}, "Id": "7206dad2-5727-427f-b34b-4bf93b4b8ebc", "Relationships": [{"Type": "CHILD", "Ids": ["5d474558-ff39-4402-829e-7dade61dd412", "3f3c22fa-0f4d-40e2-9ac6-1c2689bb5e39", "d8c1a0ef-3161-40ea-95dd-79830ae7aeed", "9b836382-fc42-49b8-b788-dd598815d090", "ee5a8d0c-520f-4a10-9e24-aa02e31a5974", "31dc158c-9bd5-48c4-b595-5c04fac78be0"]}]}, {"BlockType": "LINE", "Confidence": 97.92457580566406, "Text": "COLUMBUS, OH 43213", "Geometry": {"BoundingBox": {"Width": 0.23125481605529785, "Height": 0.02756737731397152, "Left": 0.304225891828537, "Top": 0.19232718646526337}, "Polygon": [{"X": 0.30423876643180847, "Y": 0.19232718646526337}, {"X": 0.5354807376861572, "Y": 0.1923796385526657}, {"X": 0.5354705452919006, "Y": 0.21989455819129944}, {"X": 0.304225891828537, "Y": 0.21984240412712097}]}, "Id": "8d8b1935-e13b-4442-a651-4bce47e2707c", "Relationships": [{"Type": "CHILD", "Ids": ["75f6ba0e-0c17-4f67-be6d-d1dd0ac2ebcb", "fae864a5-986f-4ea1-adc7-4a0f00f3c3b8", "a6b66fc5-26ec-4020-8532-79fbf823eb0f"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "PH:", "Geometry": {"BoundingBox": {"Width": 0.031554773449897766, "Height": 0.01434240397065878, "Left": 0.17142674326896667, "Top": 0.22778171300888062}, "Polygon": [{"X": 0.17143425345420837, "Y": 0.22778171300888062}, {"X": 0.20298151671886444, "Y": 0.22778882086277008}, {"X": 0.20297418534755707, "Y": 0.24212411046028137}, {"X": 0.17142674326896667, "Y": 0.2421170324087143}]}, "Id": "3fb3e001-3df2-4539-9e6e-92425f0fd80a", "Relationships": [{"Type": "CHILD", "Ids": ["d0b82582-361d-4e63-89fb-8c1533b7b65d"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "614-692-2055", "Geometry": {"BoundingBox": {"Width": 0.13938185572624207, "Height": 0.02168992906808853, "Left": 0.3035326302051544, "Top": 0.22282302379608154}, "Polygon": [{"X": 0.30354276299476624, "Y": 0.22282302379608154}, {"X": 0.4429144859313965, "Y": 0.22285443544387817}, {"X": 0.44290563464164734, "Y": 0.24451294541358948}, {"X": 0.3035326302051544, "Y": 0.2444816678762436}]}, "Id": "7e35547d-1889-4803-8754-2c66d9d003fc", "Relationships": [{"Type": "CHILD", "Ids": ["e1f875a7-ccf6-4e0d-b35f-c0eac78ff003"]}]}, {"BlockType": "LINE", "Confidence": 99.84135437011719, "Text": "SHIP TO", "Geometry": {"BoundingBox": {"Width": 0.07392463088035583, "Height": 0.015275117941200733, "Left": 0.17197537422180176, "Top": 0.2545347213745117}, "Polygon": [{"X": 0.17198336124420166, "Y": 0.2545347213745117}, {"X": 0.2458999902009964, "Y": 0.2545512616634369}, {"X": 0.2458924800157547, "Y": 0.2698098123073578}, {"X": 0.17197537422180176, "Y": 0.2697933316230774}]}, "Id": "97d1fa28-9d69-4722-852c-6ff1398a7db3", "Relationships": [{"Type": "CHILD", "Ids": ["26ba1ad1-fe18-40f5-b703-6acd3678b5fa", "1e6fd812-731e-452b-8551-84fc97aa7883"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "ADDRESS", "Geometry": {"BoundingBox": {"Width": 0.08604981750249863, "Height": 0.01442012470215559, "Left": 0.17269042134284973, "Top": 0.2812638580799103}, "Polygon": [{"X": 0.17269796133041382, "Y": 0.2812638580799103}, {"X": 0.25874024629592896, "Y": 0.28128302097320557}, {"X": 0.25873321294784546, "Y": 0.29568397998809814}, {"X": 0.17269042134284973, "Y": 0.29566487669944763}]}, "Id": "78711691-6086-4bf4-9b58-2db9cf2ccbed", "Relationships": [{"Type": "CHILD", "Ids": ["4253d963-2add-4cc7-aac5-149295a14351"]}]}, {"BlockType": "LINE", "Confidence": 98.72389221191406, "Text": "DLA DISPOSITION SERVICES - COLUMBUS", "Geometry": {"BoundingBox": {"Width": 0.4115481376647949, "Height": 0.02423100173473358, "Left": 0.3057725131511688, "Top": 0.26993730664253235}, "Polygon": [{"X": 0.30578380823135376, "Y": 0.26993730664253235}, {"X": 0.7173206806182861, "Y": 0.2700291872024536}, {"X": 0.7173136472702026, "Y": 0.2941683232784271}, {"X": 0.3057725131511688, "Y": 0.2940768897533417}]}, "Id": "2b1f48e8-5f6c-4475-9da4-2381973ef2c1", "Relationships": [{"Type": "CHILD", "Ids": ["84f1e89f-8223-42b9-93ac-82b8bb5183db", "44761cee-c671-4fe7-9787-6c4418646815", "1fbea871-f7bc-4d34-9364-de23e8d2ecd4", "aaeea63c-65a9-447c-911b-878cf030467e", "d9cc1ebb-2f95-41a6-ae7c-d72752884ce8"]}]}, {"BlockType": "LINE", "Confidence": 99.90872192382812, "Text": "320 N. JAMES RD, BLDG 30", "Geometry": {"BoundingBox": {"Width": 0.26637598872184753, "Height": 0.02317989431321621, "Left": 0.30567094683647156, "Top": 0.2967543303966522}, "Polygon": [{"X": 0.3056817650794983, "Y": 0.2967543303966522}, {"X": 0.5720469355583191, "Y": 0.2968134582042694}, {"X": 0.5720387697219849, "Y": 0.319934219121933}, {"X": 0.30567094683647156, "Y": 0.3198753297328949}]}, "Id": "57179dc7-af83-4035-bd5a-621105269f3f", "Relationships": [{"Type": "CHILD", "Ids": ["aa6b88a2-35ce-4644-b053-8b92ffbab646", "8462e42d-bd4e-41f4-a92e-d166bccf43d6", "aa8409f7-3051-4fdf-b3bf-6d980a24a87e", "854074a7-4c59-4f5e-9294-38d860ce3bc9", "ef32fd2e-47da-4224-8c21-b03957483ca9", "5f37895d-70cf-42e8-b0b2-3b04e5ad113a"]}]}, {"BlockType": "LINE", "Confidence": 99.09378814697266, "Text": "COLUMBUS, OH 43213", "Geometry": {"BoundingBox": {"Width": 0.22497661411762238, "Height": 0.022814173251390457, "Left": 0.30492866039276123, "Top": 0.3239269554615021}, "Polygon": [{"X": 0.30493929982185364, "Y": 0.3239269554615021}, {"X": 0.5299052596092224, "Y": 0.32397663593292236}, {"X": 0.5298967957496643, "Y": 0.3467411398887634}, {"X": 0.30492866039276123, "Y": 0.34669169783592224}]}, "Id": "eb6ab713-ea20-4c11-ad88-80aa5a663933", "Relationships": [{"Type": "CHILD", "Ids": ["96112c89-50ed-4ae7-9f61-5e35c01cd458", "4f9b48c0-e921-4f17-95ac-3a2ce737ffa7", "3e500079-b534-4b93-8aa3-63d70a5da3d6"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "REMARKS:", "Geometry": {"BoundingBox": {"Width": 0.09324616938829422, "Height": 0.014375017955899239, "Left": 0.17604592442512512, "Top": 0.3978177309036255}, "Polygon": [{"X": 0.17605341970920563, "Y": 0.3978177309036255}, {"X": 0.26929208636283875, "Y": 0.3978379964828491}, {"X": 0.2692851722240448, "Y": 0.41219276189804077}, {"X": 0.17604592442512512, "Y": 0.4121725261211395}]}, "Id": "38fd1398-b3db-460f-8265-c9a8b40a1517", "Relationships": [{"Type": "CHILD", "Ids": ["9e907ea0-abcf-4444-ba57-73ba01519961"]}]}, {"BlockType": "LINE", "Confidence": 11.671416282653809, "Text": "04", "Geometry": {"BoundingBox": {"Width": 0.054424893110990524, "Height": 0.019910182803869247, "Left": 0.6363657712936401, "Top": 0.45869338512420654}, "Polygon": [{"X": 0.6363722681999207, "Y": 0.45869338512420654}, {"X": 0.6907906532287598, "Y": 0.4587050676345825}, {"X": 0.6907846331596375, "Y": 0.4786035716533661}, {"X": 0.6363657712936401, "Y": 0.4785919487476349}]}, "Id": "e3cfcc4a-e2ea-444e-8e84-3ba7f563f6b6", "Relationships": [{"Type": "CHILD", "Ids": ["059ce9fe-f062-48f9-96a8-20238661d685"]}]}, {"BlockType": "LINE", "Confidence": 83.23561096191406, "Text": "11-01-22", "Geometry": {"BoundingBox": {"Width": 0.0875665619969368, "Height": 0.017177661880850792, "Left": 0.7373987436294556, "Top": 0.4641183912754059}, "Polygon": [{"X": 0.7374036312103271, "Y": 0.4641183912754059}, {"X": 0.8249652981758118, "Y": 0.46413716673851013}, {"X": 0.8249611258506775, "Y": 0.48129603266716003}, {"X": 0.7373987436294556, "Y": 0.48127734661102295}]}, "Id": "15a0837b-d63c-4b7e-905e-02c1fa065590", "Relationships": [{"Type": "CHILD", "Ids": ["d46049de-4e60-4fd5-aae5-074adc88d5f7"]}]}, {"BlockType": "LINE", "Confidence": 51.12344741821289, "Text": "LOOP to 0661", "Geometry": {"BoundingBox": {"Width": 0.1357785314321518, "Height": 0.019780846312642097, "Left": 0.6439326405525208, "Top": 0.5403339266777039}, "Polygon": [{"X": 0.6439390182495117, "Y": 0.5403339266777039}, {"X": 0.7797111868858337, "Y": 0.5403625965118408}, {"X": 0.7797059416770935, "Y": 0.5601148009300232}, {"X": 0.6439326405525208, "Y": 0.5600862503051758}]}, "Id": "035b92d4-ad77-44c6-bf09-683454b879d2", "Relationships": [{"Type": "CHILD", "Ids": ["2f4f0c05-268a-46f9-8947-9dc722241dba", "f27531f4-6914-48c6-bd08-b49035abe364", "3e099166-5906-4ce0-868c-926d291d4e90"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "DRIVER", "Geometry": {"BoundingBox": {"Width": 0.06685557216405869, "Height": 0.013200006447732449, "Left": 0.16791431605815887, "Top": 0.5685729384422302}, "Polygon": [{"X": 0.1679212599992752, "Y": 0.5685729384422302}, {"X": 0.23476989567279816, "Y": 0.5685869455337524}, {"X": 0.23476333916187286, "Y": 0.5817729234695435}, {"X": 0.16791431605815887, "Y": 0.581758975982666}]}, "Id": "3aa342a0-9501-476b-87f9-f3fcc5443b1f", "Relationships": [{"Type": "CHILD", "Ids": ["05abef53-8208-4648-b230-614bd0a22c59"]}]}, {"BlockType": "LINE", "Confidence": 99.69168090820312, "Text": "SIGNATURE:", "Geometry": {"BoundingBox": {"Width": 0.11224745213985443, "Height": 0.014481700025498867, "Left": 0.17075137794017792, "Top": 0.5916962027549744}, "Polygon": [{"X": 0.17075896263122559, "Y": 0.5916962027549744}, {"X": 0.28299883008003235, "Y": 0.5917195677757263}, {"X": 0.2829919457435608, "Y": 0.6061778664588928}, {"X": 0.17075137794017792, "Y": 0.6061545014381409}]}, "Id": "0b98ac00-32d1-422c-9d94-70a9b3b3a80b", "Relationships": [{"Type": "CHILD", "Ids": ["f84283f0-7eeb-4313-9403-2ae9be9d4eb7"]}]}, {"BlockType": "LINE", "Confidence": 32.05755615234375, "Text": "INSOLND 32490 15", "Geometry": {"BoundingBox": {"Width": 0.20308881998062134, "Height": 0.01938629522919655, "Left": 0.6445860862731934, "Top": 0.6210573315620422}, "Polygon": [{"X": 0.6445923447608948, "Y": 0.6210573315620422}, {"X": 0.8476749062538147, "Y": 0.6210994124412537}, {"X": 0.8476703763008118, "Y": 0.6404436230659485}, {"X": 0.6445860862731934, "Y": 0.6404016613960266}]}, "Id": "46466636-0406-493c-bf1f-c93b6475f8ea", "Relationships": [{"Type": "CHILD", "Ids": ["48afb48d-7184-4445-8129-b6309cc8a6f9", "163c394b-3474-4b15-8f0c-388f60396f79", "4bf1e189-a732-45bd-9b21-7dd4f8dbdb9b"]}]}, {"BlockType": "LINE", "Confidence": 99.88121795654297, "Text": "WEIGHER:", "Geometry": {"BoundingBox": {"Width": 0.09482968598604202, "Height": 0.01397744845598936, "Left": 0.16569936275482178, "Top": 0.6442453265190125}, "Polygon": [{"X": 0.16570672392845154, "Y": 0.6442453265190125}, {"X": 0.2605290412902832, "Y": 0.6442648768424988}, {"X": 0.2605222761631012, "Y": 0.6582227945327759}, {"X": 0.16569936275482178, "Y": 0.6582033038139343}]}, "Id": "e265b4d4-5a63-46ff-aa7a-f9a78cf1ca2f", "Relationships": [{"Type": "CHILD", "Ids": ["63cc7e53-1425-49b4-82e5-5f783f16fc65"]}]}, {"BlockType": "LINE", "Confidence": 84.5360336303711, "Text": "12:44", "Geometry": {"BoundingBox": {"Width": 0.055771127343177795, "Height": 0.017212191596627235, "Left": 0.5562214255332947, "Top": 0.6670264005661011}, "Polygon": [{"X": 0.5562276244163513, "Y": 0.6670264005661011}, {"X": 0.6119925379753113, "Y": 0.667037844657898}, {"X": 0.6119867563247681, "Y": 0.684238612651825}, {"X": 0.5562214255332947, "Y": 0.6842271685600281}]}, "Id": "35b7a6c0-b3e5-4df8-842e-86be447ddefb", "Relationships": [{"Type": "CHILD", "Ids": ["4e3bafa4-eb37-4baa-9554-527ddcc76405"]}]}, {"BlockType": "LINE", "Confidence": 98.08374786376953, "Text": "3152", "Geometry": {"BoundingBox": {"Width": 0.0459548644721508, "Height": 0.016446229070425034, "Left": 0.5659252405166626, "Top": 0.6871525049209595}, "Polygon": [{"X": 0.5659310817718506, "Y": 0.6871525049209595}, {"X": 0.6118801236152649, "Y": 0.6871618628501892}, {"X": 0.6118745803833008, "Y": 0.70359867811203}, {"X": 0.5659252405166626, "Y": 0.7035893797874451}]}, "Id": "8d1290e0-468d-4fd7-acda-513343150128", "Relationships": [{"Type": "CHILD", "Ids": ["d79e90a9-7135-4f0a-8638-64499f59534e"]}]}, {"BlockType": "LINE", "Confidence": 41.24043273925781, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.39087897539138794, "Height": 0.06406599283218384, "Left": 0.2457098662853241, "Top": 0.7298856973648071}, "Polygon": [{"X": 0.24574139714241028, "Y": 0.7298856973648071}, {"X": 0.6365888714790344, "Y": 0.7299648523330688}, {"X": 0.636568009853363, "Y": 0.793951690196991}, {"X": 0.2457098662853241, "Y": 0.7938737273216248}]}, "Id": "dd2490bd-ce75-43d5-bd23-bd12b5b79b5d", "Relationships": [{"Type": "CHILD", "Ids": ["7d07ea88-5777-4bb5-a185-f2615b574805", "0633d51e-762c-4df5-a8a5-025231142788"]}]}, {"BlockType": "LINE", "Confidence": 90.63078308105469, "Text": "14:57", "Geometry": {"BoundingBox": {"Width": 0.055600397288799286, "Height": 0.01779528707265854, "Left": 0.64554762840271, "Top": 0.7462238669395447}, "Polygon": [{"X": 0.6455533504486084, "Y": 0.7462238669395447}, {"X": 0.7011480331420898, "Y": 0.7462350726127625}, {"X": 0.7011427283287048, "Y": 0.7640191316604614}, {"X": 0.64554762840271, "Y": 0.7640079855918884}]}, "Id": "ce9859eb-9335-4450-bab5-f697c59c3260", "Relationships": [{"Type": "CHILD", "Ids": ["1b49c517-470f-426a-8c45-e722ebe487f6"]}]}, {"BlockType": "LINE", "Confidence": 83.47676086425781, "Text": "11-01-22", "Geometry": {"BoundingBox": {"Width": 0.08937840908765793, "Height": 0.018076036125421524, "Left": 0.7448363900184631, "Top": 0.7487182021141052}, "Polygon": [{"X": 0.744841456413269, "Y": 0.7487182021141052}, {"X": 0.8342148065567017, "Y": 0.7487362027168274}, {"X": 0.8342104554176331, "Y": 0.7667942643165588}, {"X": 0.7448363900184631, "Y": 0.7667763233184814}]}, "Id": "f02058a0-7a42-47a8-bda6-e602295fa31a", "Relationships": [{"Type": "CHILD", "Ids": ["f3715ad4-2348-4812-823d-fad53fd24003"]}]}, {"BlockType": "LINE", "Confidence": 92.61419677734375, "Text": "11/2/2020", "Geometry": {"BoundingBox": {"Width": 0.14967866241931915, "Height": 0.03506859764456749, "Left": 0.4705485999584198, "Top": 0.7740284204483032}, "Polygon": [{"X": 0.47056248784065247, "Y": 0.7740284204483032}, {"X": 0.6202272772789001, "Y": 0.7740584015846252}, {"X": 0.6202155947685242, "Y": 0.8090969920158386}, {"X": 0.4705485999584198, "Y": 0.8090672492980957}]}, "Id": "43be3d81-224a-4779-8ad4-f57bedece575", "Relationships": [{"Type": "CHILD", "Ids": ["db0d92dd-a23f-49c4-bf30-dcf3b6e1ef82"]}]}, {"BlockType": "LINE", "Confidence": 48.84908676147461, "Text": "58260 In GROSS", "Geometry": {"BoundingBox": {"Width": 0.15591120719909668, "Height": 0.01798521727323532, "Left": 0.66522616147995, "Top": 0.8069742918014526}, "Polygon": [{"X": 0.6652317643165588, "Y": 0.8069742918014526}, {"X": 0.8211373686790466, "Y": 0.8070053458213806}, {"X": 0.8211329579353333, "Y": 0.8249595165252686}, {"X": 0.66522616147995, "Y": 0.8249286413192749}]}, "Id": "2812201e-4a23-467f-a681-a5b2a104043e", "Relationships": [{"Type": "CHILD", "Ids": ["0b0e1ab0-3bff-44b9-8112-fd908c30217e", "6ce6e978-92fa-48f3-a15b-a59e80760bc3", "e68f9151-06d6-4200-87cf-99725eefd292"]}]}, {"BlockType": "LINE", "Confidence": 35.57916259765625, "Text": "PA", "Geometry": {"BoundingBox": {"Width": 0.3912660479545593, "Height": 0.10972226411104202, "Left": 0.15533116459846497, "Top": 0.8131336569786072}, "Polygon": [{"X": 0.15538941323757172, "Y": 0.8131336569786072}, {"X": 0.5465972423553467, "Y": 0.8132113218307495}, {"X": 0.5465573072433472, "Y": 0.9228559136390686}, {"X": 0.15533116459846497, "Y": 0.9227801561355591}]}, "Id": "43ad4153-4c89-4d47-abf3-6640a2a897db", "Relationships": [{"Type": "CHILD", "Ids": ["b260c5cc-f587-410a-b907-44d602f0b936"]}]}, {"BlockType": "LINE", "Confidence": 42.23014831542969, "Text": "400", "Geometry": {"BoundingBox": {"Width": 0.034365251660346985, "Height": 0.016346897929906845, "Left": 0.6903668642044067, "Top": 0.8265507817268372}, "Polygon": [{"X": 0.6903718113899231, "Y": 0.8265507817268372}, {"X": 0.7247321605682373, "Y": 0.8265575766563416}, {"X": 0.7247274518013, "Y": 0.8428977131843567}, {"X": 0.6903668642044067, "Y": 0.8428909182548523}]}, "Id": "1be70b73-5eae-40f4-8598-34aaf392765e", "Relationships": [{"Type": "CHILD", "Ids": ["0e11d861-d688-4f5d-b150-99e5b8104fdd"]}]}, {"BlockType": "LINE", "Confidence": 27.813297271728516, "Text": "5", "Geometry": {"BoundingBox": {"Width": 0.021958019584417343, "Height": 0.015524042770266533, "Left": 0.7364304065704346, "Top": 0.8285086750984192}, "Polygon": [{"X": 0.736434817314148, "Y": 0.8285086750984192}, {"X": 0.7583884596824646, "Y": 0.8285130262374878}, {"X": 0.7583842277526855, "Y": 0.8440327048301697}, {"X": 0.7364304065704346, "Y": 0.8440284132957458}]}, "Id": "aa680c22-c2d1-441f-bacf-26d15921976a", "Relationships": [{"Type": "CHILD", "Ids": ["a515967c-4f9f-4cbe-a64b-98aba7d590d5"]}]}, {"BlockType": "LINE", "Confidence": 72.61260223388672, "Text": "TARE", "Geometry": {"BoundingBox": {"Width": 0.04690805822610855, "Height": 0.01722428947687149, "Left": 0.766647219657898, "Top": 0.8278085589408875}, "Polygon": [{"X": 0.7666518688201904, "Y": 0.8278085589408875}, {"X": 0.8135553002357483, "Y": 0.8278177976608276}, {"X": 0.8135509490966797, "Y": 0.845032811164856}, {"X": 0.766647219657898, "Y": 0.8450235724449158}]}, "Id": "3b03a192-ba56-47e0-b38b-b6ea2de240f0", "Relationships": [{"Type": "CHILD", "Ids": ["cefb38a6-2e97-4618-a9fe-e8fecdb7f878"]}]}, {"BlockType": "LINE", "Confidence": 61.77196502685547, "Text": "in NET", "Geometry": {"BoundingBox": {"Width": 0.06661691516637802, "Height": 0.01607624627649784, "Left": 0.7357911467552185, "Top": 0.8482106328010559}, "Polygon": [{"X": 0.7357957363128662, "Y": 0.8482106328010559}, {"X": 0.8024080991744995, "Y": 0.8482237458229065}, {"X": 0.80240398645401, "Y": 0.8642868995666504}, {"X": 0.7357911467552185, "Y": 0.8642738461494446}]}, "Id": "a2bab5e9-a751-4608-9782-ed3dbf42eb34", "Relationships": [{"Type": "CHILD", "Ids": ["597a7c5f-6e0c-488d-82fe-5cadf30ee96f", "15ac7840-b2e1-4bd2-8dec-830b04a3507a"]}]}, {"BlockType": "LINE", "Confidence": 21.733198165893555, "Text": "&", "Geometry": {"BoundingBox": {"Width": 0.01951032504439354, "Height": 0.01703607477247715, "Left": 0.5947649478912354, "Top": 0.8667954206466675}, "Polygon": [{"X": 0.5947707891464233, "Y": 0.8667954206466675}, {"X": 0.6142752766609192, "Y": 0.8667992353439331}, {"X": 0.6142695546150208, "Y": 0.8838315010070801}, {"X": 0.5947649478912354, "Y": 0.8838276863098145}]}, "Id": "a808fa7c-3199-4863-8cef-844605a8729a", "Relationships": [{"Type": "CHILD", "Ids": ["5800b8be-84f7-4009-a20f-e296d74d1dc6"]}]}, {"BlockType": "LINE", "Confidence": 89.41107177734375, "Text": "11-2-22", "Geometry": {"BoundingBox": {"Width": 0.1454796940088272, "Height": 0.024066345766186714, "Left": 0.3973536789417267, "Top": 0.9159774780273438}, "Polygon": [{"X": 0.39736396074295044, "Y": 0.9159774780273438}, {"X": 0.5428333878517151, "Y": 0.9160057306289673}, {"X": 0.5428245663642883, "Y": 0.9400438666343689}, {"X": 0.3973536789417267, "Y": 0.9400157928466797}]}, "Id": "827202d0-a379-45fd-9888-9486754bc127", "Relationships": [{"Type": "CHILD", "Ids": ["96a9aea8-f0ac-4899-aa06-f6941bf64328"]}]}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "ELECTRONIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14704151451587677, "Height": 0.024841424077749252, "Left": 0.3993188440799713, "Top": 0.010396572761237621}, "Polygon": [{"X": 0.39932942390441895, "Y": 0.010396572761237621}, {"X": 0.5463603734970093, "Y": 0.010431132279336452}, {"X": 0.5463513135910034, "Y": 0.03523799777030945}, {"X": 0.3993188440799713, "Y": 0.03520360216498375}]}, "Id": "87f52016-c4e0-4013-bbaf-b55fab773917"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "SCALE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0724017471075058, "Height": 0.017427686601877213, "Left": 0.5528454780578613, "Top": 0.011306204833090305}, "Polygon": [{"X": 0.5528517961502075, "Y": 0.011306204833090305}, {"X": 0.6252472400665283, "Y": 0.011323218233883381}, {"X": 0.6252414584159851, "Y": 0.028733890503644943}, {"X": 0.5528454780578613, "Y": 0.028716934844851494}]}, "Id": "06c8967c-aa65-4f8c-b28a-9ded93a928b8"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "TICKET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08304726332426071, "Height": 0.017754187807440758, "Left": 0.6322442889213562, "Top": 0.011528587900102139}, "Polygon": [{"X": 0.6322500705718994, "Y": 0.011528587900102139}, {"X": 0.7152915596961975, "Y": 0.011548102833330631}, {"X": 0.715286374092102, "Y": 0.02928277477622032}, {"X": 0.6322442889213562, "Y": 0.0292633268982172}]}, "Id": "a5d094a9-14bd-48ed-9e57-f91964f4bf48"}, {"BlockType": "WORD", "Confidence": 68.63221740722656, "Text": "EIL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07379066944122314, "Height": 0.027249764651060104, "Left": 0.7669656872749329, "Top": 0.0038279935251921415}, "Polygon": [{"X": 0.7669730186462402, "Y": 0.0038279935251921415}, {"X": 0.840756356716156, "Y": 0.0038453585002571344}, {"X": 0.8407498598098755, "Y": 0.031077757477760315}, {"X": 0.7669656872749329, "Y": 0.031060483306646347}]}, "Id": "e5a5ea4d-1dcb-4a70-b556-263795997cf0"}, {"BlockType": "WORD", "Confidence": 73.07328033447266, "Text": "NC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06511225551366806, "Height": 0.030571557581424713, "Left": 0.7649180293083191, "Top": 0.03962692245841026}, "Polygon": [{"X": 0.7649262547492981, "Y": 0.03962692245841026}, {"X": 0.8300302624702454, "Y": 0.03964213654398918}, {"X": 0.8300228118896484, "Y": 0.07019847631454468}, {"X": 0.7649180293083191, "Y": 0.07018335163593292}]}, "Id": "7299c1a6-e736-41a7-8595-7ab26e72ab86"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0560796782374382, "Height": 0.016097577288746834, "Left": 0.16496825218200684, "Top": 0.06779459118843079}, "Polygon": [{"X": 0.16497673094272614, "Y": 0.06779459118843079}, {"X": 0.22104793787002563, "Y": 0.0678076222538948}, {"X": 0.22103984653949738, "Y": 0.08389216661453247}, {"X": 0.16496825218200684, "Y": 0.08387918025255203}]}, "Id": "dec161ae-c112-4fff-8a81-e8de28e494b7"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "MAILING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08374965935945511, "Height": 0.016218237578868866, "Left": 0.16687875986099243, "Top": 0.1219445988535881}, "Polygon": [{"X": 0.1668872833251953, "Y": 0.1219445988535881}, {"X": 0.25062841176986694, "Y": 0.12196385115385056}, {"X": 0.2506204843521118, "Y": 0.13816283643245697}, {"X": 0.16687875986099243, "Y": 0.1381436437368393}]}, "Id": "e28267be-00a7-46de-9a42-f3b186bc858b"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ADDRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08829937130212784, "Height": 0.016302989795804024, "Left": 0.16692252457141876, "Top": 0.14857086539268494}, "Polygon": [{"X": 0.16693109273910522, "Y": 0.14857086539268494}, {"X": 0.2552219033241272, "Y": 0.1485910713672638}, {"X": 0.2552139461040497, "Y": 0.1648738533258438}, {"X": 0.16692252457141876, "Y": 0.16485372185707092}]}, "Id": "c368cb48-9135-49af-bb44-8211462e561c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "DLA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03958554193377495, "Height": 0.014810402877628803, "Left": 0.3036046624183655, "Top": 0.15100052952766418}, "Polygon": [{"X": 0.3036115765571594, "Y": 0.15100052952766418}, {"X": 0.34319019317626953, "Y": 0.15100957453250885}, {"X": 0.3431835174560547, "Y": 0.16581092774868011}, {"X": 0.3036046624183655, "Y": 0.16580189764499664}]}, "Id": "e4bafa10-8b91-4cee-9f88-fba445efe28f"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "DISPOSITION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12665703892707825, "Height": 0.02270433120429516, "Left": 0.3484846353530884, "Top": 0.1435346156358719}, "Polygon": [{"X": 0.3484947979450226, "Y": 0.1435346156358719}, {"X": 0.4751416742801666, "Y": 0.14356361329555511}, {"X": 0.47513270378112793, "Y": 0.1662389487028122}, {"X": 0.3484846353530884, "Y": 0.16621007025241852}]}, "Id": "626657d6-b51b-444b-8389-1ea83f2fe602"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "SERVICES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0963425561785698, "Height": 0.015337194316089153, "Left": 0.4805351197719574, "Top": 0.14141671359539032}, "Polygon": [{"X": 0.4805411100387573, "Y": 0.14141671359539032}, {"X": 0.5768776535987854, "Y": 0.1414387822151184}, {"X": 0.5768722891807556, "Y": 0.15675391256809235}, {"X": 0.4805351197719574, "Y": 0.15673190355300903}]}, "Id": "f10f30f5-a463-43e2-b4db-c6ea8afebab8"}, {"BlockType": "WORD", "Confidence": 95.11579132080078, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007777062710374594, "Height": 0.003729692194610834, "Left": 0.5825867056846619, "Top": 0.14858977496623993}, "Polygon": [{"X": 0.5825880169868469, "Y": 0.14858977496623993}, {"X": 0.5903638005256653, "Y": 0.1485915631055832}, {"X": 0.5903624892234802, "Y": 0.15231947600841522}, {"X": 0.5825867056846619, "Y": 0.15231770277023315}]}, "Id": "e3d29e27-819a-4750-ac4f-ffc255d5bdc0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "COLUMBUS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12067362666130066, "Height": 0.014433598145842552, "Left": 0.5966482162475586, "Top": 0.1422593891620636}, "Polygon": [{"X": 0.596653163433075, "Y": 0.1422593891620636}, {"X": 0.7173218131065369, "Y": 0.14228703081607819}, {"X": 0.7173176407814026, "Y": 0.1566929817199707}, {"X": 0.5966482162475586, "Y": 0.15666542947292328}]}, "Id": "8597ddca-a0e5-43ab-8677-fad914edc080"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "3990", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04977942630648613, "Height": 0.015109634026885033, "Left": 0.3036785423755646, "Top": 0.1768990308046341}, "Polygon": [{"X": 0.30368560552597046, "Y": 0.1768990308046341}, {"X": 0.3534579575061798, "Y": 0.17691035568714142}, {"X": 0.3534512221813202, "Y": 0.19200865924358368}, {"X": 0.3036785423755646, "Y": 0.19199737906455994}]}, "Id": "5d474558-ff39-4402-829e-7dade61dd412"}, {"BlockType": "WORD", "Confidence": 97.25447082519531, "Text": "E.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015978273004293442, "Height": 0.013894418254494667, "Left": 0.35990235209465027, "Top": 0.17658782005310059}, "Polygon": [{"X": 0.3599085211753845, "Y": 0.17658782005310059}, {"X": 0.375880628824234, "Y": 0.17659145593643188}, {"X": 0.3758745491504669, "Y": 0.1904822438955307}, {"X": 0.35990235209465027, "Y": 0.1904786080121994}]}, "Id": "3f3c22fa-0f4d-40e2-9ac6-1c2689bb5e39"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "BROAD", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06965865939855576, "Height": 0.018472004681825638, "Left": 0.3827837407588959, "Top": 0.1710250824689865}, "Polygon": [{"X": 0.38279175758361816, "Y": 0.1710250824689865}, {"X": 0.45244237780570984, "Y": 0.17104095220565796}, {"X": 0.4524349272251129, "Y": 0.18949709832668304}, {"X": 0.3827837407588959, "Y": 0.18948128819465637}]}, "Id": "d8c1a0ef-3161-40ea-95dd-79830ae7aeed"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "ST,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02897030860185623, "Height": 0.016793159767985344, "Left": 0.45798707008361816, "Top": 0.16899298131465912}, "Polygon": [{"X": 0.45799383521080017, "Y": 0.16899298131465912}, {"X": 0.4869574010372162, "Y": 0.168999582529068}, {"X": 0.4869508445262909, "Y": 0.1857861429452896}, {"X": 0.45798707008361816, "Y": 0.18577957153320312}]}, "Id": "9b836382-fc42-49b8-b788-dd598815d090"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "BLDG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05420282110571861, "Height": 0.014812265522778034, "Left": 0.49435052275657654, "Top": 0.16702425479888916}, "Polygon": [{"X": 0.4943562150001526, "Y": 0.16702425479888916}, {"X": 0.5485533475875854, "Y": 0.16703660786151886}, {"X": 0.5485479831695557, "Y": 0.18183653056621552}, {"X": 0.49435052275657654, "Y": 0.1818242073059082}]}, "Id": "ee5a8d0c-520f-4a10-9e24-aa02e31a5974"}, {"BlockType": "WORD", "Confidence": 99.68669891357422, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02583177760243416, "Height": 0.014258000999689102, "Left": 0.5550350546836853, "Top": 0.16708414256572723}, "Polygon": [{"X": 0.5550402402877808, "Y": 0.16708414256572723}, {"X": 0.5808668732643127, "Y": 0.16709004342556}, {"X": 0.5808618664741516, "Y": 0.18134215474128723}, {"X": 0.5550350546836853, "Y": 0.18133628368377686}]}, "Id": "31dc158c-9bd5-48c4-b595-5c04fac78be0"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "COLUMBUS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11954750120639801, "Height": 0.02146313339471817, "Left": 0.304225891828537, "Top": 0.19840623438358307}, "Polygon": [{"X": 0.30423590540885925, "Y": 0.19840623438358307}, {"X": 0.4237733781337738, "Y": 0.1984333097934723}, {"X": 0.4237644672393799, "Y": 0.21986937522888184}, {"X": 0.304225891828537, "Y": 0.21984240412712097}]}, "Id": "75f6ba0e-0c17-4f67-be6d-d1dd0ac2ebcb"}, {"BlockType": "WORD", "Confidence": 94.43020629882812, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030667968094348907, "Height": 0.015181094408035278, "Left": 0.4297281503677368, "Top": 0.19649232923984528}, "Polygon": [{"X": 0.4297344386577606, "Y": 0.19649232923984528}, {"X": 0.4603961408138275, "Y": 0.1964992880821228}, {"X": 0.46039003133773804, "Y": 0.21167342364788055}, {"X": 0.4297281503677368, "Y": 0.2116665095090866}]}, "Id": "fae864a5-986f-4ea1-adc7-4a0f00f3c3b8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "43213", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06477638334035873, "Height": 0.017509449273347855, "Left": 0.4707043170928955, "Top": 0.19236494600772858}, "Polygon": [{"X": 0.47071126103401184, "Y": 0.19236494600772858}, {"X": 0.5354807376861572, "Y": 0.1923796385526657}, {"X": 0.5354742407798767, "Y": 0.20987439155578613}, {"X": 0.4707043170928955, "Y": 0.20985975861549377}]}, "Id": "a6b66fc5-26ec-4020-8532-79fbf823eb0f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PH:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031554773449897766, "Height": 0.01434240397065878, "Left": 0.17142674326896667, "Top": 0.22778171300888062}, "Polygon": [{"X": 0.17143425345420837, "Y": 0.22778171300888062}, {"X": 0.20298151671886444, "Y": 0.22778882086277008}, {"X": 0.20297418534755707, "Y": 0.24212411046028137}, {"X": 0.17142674326896667, "Y": 0.2421170324087143}]}, "Id": "d0b82582-361d-4e63-89fb-8c1533b7b65d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "614-692-2055", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13938185572624207, "Height": 0.02168992906808853, "Left": 0.3035326302051544, "Top": 0.22282302379608154}, "Polygon": [{"X": 0.30354276299476624, "Y": 0.22282302379608154}, {"X": 0.4429144859313965, "Y": 0.22285443544387817}, {"X": 0.44290563464164734, "Y": 0.24451294541358948}, {"X": 0.3035326302051544, "Y": 0.2444816678762436}]}, "Id": "e1f875a7-ccf6-4e0d-b35f-c0eac78ff003"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "SHIP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042304426431655884, "Height": 0.014471771195530891, "Left": 0.17197579145431519, "Top": 0.2545347213745117}, "Polygon": [{"X": 0.17198336124420166, "Y": 0.2545347213745117}, {"X": 0.21428021788597107, "Y": 0.2545441687107086}, {"X": 0.2142728865146637, "Y": 0.26900649070739746}, {"X": 0.17197579145431519, "Y": 0.26899704337120056}]}, "Id": "26ba1ad1-fe18-40f5-b703-6acd3678b5fa"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02669389545917511, "Height": 0.014171546325087547, "Left": 0.2192055583000183, "Top": 0.2556382715702057}, "Polygon": [{"X": 0.21921269595623016, "Y": 0.2556382715702057}, {"X": 0.24589945375919342, "Y": 0.2556442618370056}, {"X": 0.2458924800157547, "Y": 0.2698098123073578}, {"X": 0.2192055583000183, "Y": 0.26980385184288025}]}, "Id": "1e6fd812-731e-452b-8551-84fc97aa7883"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "ADDRESS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08604981750249863, "Height": 0.01442012470215559, "Left": 0.17269042134284973, "Top": 0.2812638580799103}, "Polygon": [{"X": 0.17269796133041382, "Y": 0.2812638580799103}, {"X": 0.25874024629592896, "Y": 0.28128302097320557}, {"X": 0.25873321294784546, "Y": 0.29568397998809814}, {"X": 0.17269042134284973, "Y": 0.29566487669944763}]}, "Id": "4253d963-2add-4cc7-aac5-149295a14351"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "DLA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038502130657434464, "Height": 0.014192958362400532, "Left": 0.3057725131511688, "Top": 0.2798924744129181}, "Polygon": [{"X": 0.3057791590690613, "Y": 0.2798924744129181}, {"X": 0.3442746698856354, "Y": 0.27990105748176575}, {"X": 0.344268262386322, "Y": 0.29408544301986694}, {"X": 0.3057725131511688, "Y": 0.2940768897533417}]}, "Id": "84f1e89f-8223-42b9-93ac-82b8bb5183db"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "DISPOSITION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12701693177223206, "Height": 0.020233789458870888, "Left": 0.3492412567138672, "Top": 0.2734990417957306}, "Polygon": [{"X": 0.34925034642219543, "Y": 0.2734990417957306}, {"X": 0.47625818848609924, "Y": 0.2735273838043213}, {"X": 0.47625023126602173, "Y": 0.29373282194137573}, {"X": 0.3492412567138672, "Y": 0.293704628944397}]}, "Id": "44761cee-c671-4fe7-9787-6c4418646815"}, {"BlockType": "WORD", "Confidence": 99.658203125, "Text": "SERVICES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09675949066877365, "Height": 0.0165406484156847, "Left": 0.48094213008880615, "Top": 0.27088695764541626}, "Polygon": [{"X": 0.48094862699508667, "Y": 0.27088695764541626}, {"X": 0.5777016282081604, "Y": 0.27090853452682495}, {"X": 0.5776958465576172, "Y": 0.2874276041984558}, {"X": 0.48094213008880615, "Y": 0.2874060571193695}]}, "Id": "1fbea871-f7bc-4d34-9364-de23e8d2ecd4"}, {"BlockType": "WORD", "Confidence": 94.19642639160156, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00809548981487751, "Height": 0.0036270671989768744, "Left": 0.5829697251319885, "Top": 0.277271032333374}, "Polygon": [{"X": 0.5829709768295288, "Y": 0.277271032333374}, {"X": 0.5910652279853821, "Y": 0.2772728502750397}, {"X": 0.5910639762878418, "Y": 0.2808981239795685}, {"X": 0.5829697251319885, "Y": 0.28089630603790283}]}, "Id": "aaeea63c-65a9-447c-911b-878cf030467e"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "COLUMBUS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12040164321660995, "Height": 0.01643431931734085, "Left": 0.596919059753418, "Top": 0.2700023353099823}, "Polygon": [{"X": 0.5969246625900269, "Y": 0.2700023353099823}, {"X": 0.7173206806182861, "Y": 0.2700291872024536}, {"X": 0.7173159122467041, "Y": 0.28643664717674255}, {"X": 0.596919059753418, "Y": 0.286409854888916}]}, "Id": "d9cc1ebb-2f95-41a6-ae7c-d72752884ce8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "320", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03642487898468971, "Height": 0.01473768800497055, "Left": 0.30567094683647156, "Top": 0.3051457107067108}, "Polygon": [{"X": 0.3056778311729431, "Y": 0.3051457107067108}, {"X": 0.34209582209587097, "Y": 0.3051537871360779}, {"X": 0.3420891761779785, "Y": 0.31988340616226196}, {"X": 0.30567094683647156, "Y": 0.3198753297328949}]}, "Id": "aa6b88a2-35ce-4644-b053-8b92ffbab646"}, {"BlockType": "WORD", "Confidence": 99.73552703857422, "Text": "N.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019578561186790466, "Height": 0.013876480981707573, "Left": 0.34823429584503174, "Top": 0.3045549988746643}, "Polygon": [{"X": 0.34824052453041077, "Y": 0.3045549988746643}, {"X": 0.3678128719329834, "Y": 0.3045593500137329}, {"X": 0.3678067624568939, "Y": 0.3184314966201782}, {"X": 0.34823429584503174, "Y": 0.318427175283432}]}, "Id": "8462e42d-bd4e-41f4-a92e-d166bccf43d6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "JAMES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06625821441411972, "Height": 0.017166364938020706, "Left": 0.3734114170074463, "Top": 0.30113789439201355}, "Polygon": [{"X": 0.373418927192688, "Y": 0.30113789439201355}, {"X": 0.4396696388721466, "Y": 0.30115261673927307}, {"X": 0.4396626055240631, "Y": 0.31830427050590515}, {"X": 0.3734114170074463, "Y": 0.3182896375656128}]}, "Id": "aa8409f7-3051-4fdf-b3bf-6d980a24a87e"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "RD,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03326067700982094, "Height": 0.01660206913948059, "Left": 0.4454376995563507, "Top": 0.29998311400413513}, "Polygon": [{"X": 0.4454444646835327, "Y": 0.29998311400413513}, {"X": 0.47869837284088135, "Y": 0.2999904751777649}, {"X": 0.47869184613227844, "Y": 0.3165851831436157}, {"X": 0.4454376995563507, "Y": 0.31657782196998596}]}, "Id": "854074a7-4c59-4f5e-9294-38d860ce3bc9"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "BLDG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05386074259877205, "Height": 0.01557063590735197, "Left": 0.48580920696258545, "Top": 0.29747021198272705}, "Polygon": [{"X": 0.48581525683403015, "Y": 0.29747021198272705}, {"X": 0.539669930934906, "Y": 0.2974821627140045}, {"X": 0.5396642088890076, "Y": 0.3130408227443695}, {"X": 0.48580920696258545, "Y": 0.3130289316177368}]}, "Id": "ef32fd2e-47da-4224-8c21-b03957483ca9"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025886572897434235, "Height": 0.015033701434731483, "Left": 0.5461603403091431, "Top": 0.2968077063560486}, "Polygon": [{"X": 0.5461658239364624, "Y": 0.2968077063560486}, {"X": 0.5720469355583191, "Y": 0.2968134582042694}, {"X": 0.5720416307449341, "Y": 0.3118414282798767}, {"X": 0.5461603403091431, "Y": 0.3118356764316559}]}, "Id": "5f37895d-70cf-42e8-b0b2-3b04e5ad113a"}, {"BlockType": "WORD", "Confidence": 99.59163665771484, "Text": "COLUMBUS,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11958572268486023, "Height": 0.01907452568411827, "Left": 0.30492866039276123, "Top": 0.3276434540748596}, "Polygon": [{"X": 0.30493757128715515, "Y": 0.3276434540748596}, {"X": 0.42451438307762146, "Y": 0.32766982913017273}, {"X": 0.42450645565986633, "Y": 0.3467179834842682}, {"X": 0.30492866039276123, "Y": 0.34669169783592224}]}, "Id": "96112c89-50ed-4ae7-9f61-5e35c01cd458"}, {"BlockType": "WORD", "Confidence": 97.71903228759766, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030391449108719826, "Height": 0.014730686321854591, "Left": 0.4303402900695801, "Top": 0.3261784017086029}, "Polygon": [{"X": 0.43034636974334717, "Y": 0.3261784017086029}, {"X": 0.46073171496391296, "Y": 0.32618510723114014}, {"X": 0.4607258141040802, "Y": 0.34090909361839294}, {"X": 0.4303402900695801, "Y": 0.3409024178981781}]}, "Id": "4f9b48c0-e921-4f17-95ac-3a2ce737ffa7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "43213", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06362996250391006, "Height": 0.015708114951848984, "Left": 0.46627530455589294, "Top": 0.32396259903907776}, "Polygon": [{"X": 0.46628156304359436, "Y": 0.32396259903907776}, {"X": 0.5299052596092224, "Y": 0.32397663593292236}, {"X": 0.5298994183540344, "Y": 0.33967071771621704}, {"X": 0.46627530455589294, "Y": 0.3396567106246948}]}, "Id": "3e500079-b534-4b93-8aa3-63d70a5da3d6"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "REMARKS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09324616938829422, "Height": 0.014375017955899239, "Left": 0.17604592442512512, "Top": 0.3978177309036255}, "Polygon": [{"X": 0.17605341970920563, "Y": 0.3978177309036255}, {"X": 0.26929208636283875, "Y": 0.3978379964828491}, {"X": 0.2692851722240448, "Y": 0.41219276189804077}, {"X": 0.17604592442512512, "Y": 0.4121725261211395}]}, "Id": "9e907ea0-abcf-4444-ba57-73ba01519961"}, {"BlockType": "WORD", "Confidence": 11.671416282653809, "Text": "04", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054424893110990524, "Height": 0.019910182803869247, "Left": 0.6363657712936401, "Top": 0.45869338512420654}, "Polygon": [{"X": 0.6363722681999207, "Y": 0.45869338512420654}, {"X": 0.6907906532287598, "Y": 0.4587050676345825}, {"X": 0.6907846331596375, "Y": 0.4786035716533661}, {"X": 0.6363657712936401, "Y": 0.4785919487476349}]}, "Id": "059ce9fe-f062-48f9-96a8-20238661d685"}, {"BlockType": "WORD", "Confidence": 83.23561096191406, "Text": "11-01-22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0875665619969368, "Height": 0.017177661880850792, "Left": 0.7373987436294556, "Top": 0.4641183912754059}, "Polygon": [{"X": 0.7374036312103271, "Y": 0.4641183912754059}, {"X": 0.8249652981758118, "Y": 0.46413716673851013}, {"X": 0.8249611258506775, "Y": 0.48129603266716003}, {"X": 0.7373987436294556, "Y": 0.48127734661102295}]}, "Id": "d46049de-4e60-4fd5-aae5-074adc88d5f7"}, {"BlockType": "WORD", "Confidence": 90.07015228271484, "Text": "LOOP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0488399937748909, "Height": 0.01895984448492527, "Left": 0.6439329385757446, "Top": 0.5403339266777039}, "Polygon": [{"X": 0.6439390182495117, "Y": 0.5403339266777039}, {"X": 0.6927729249000549, "Y": 0.54034423828125}, {"X": 0.6927672028541565, "Y": 0.559293806552887}, {"X": 0.6439329385757446, "Y": 0.5592835545539856}]}, "Id": "2f4f0c05-268a-46f9-8947-9dc722241dba"}, {"BlockType": "WORD", "Confidence": 41.240631103515625, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02260267548263073, "Height": 0.01715119183063507, "Left": 0.7030095458030701, "Top": 0.5419790744781494}, "Polygon": [{"X": 0.7030146718025208, "Y": 0.5419790744781494}, {"X": 0.725612223148346, "Y": 0.5419838428497314}, {"X": 0.7256073355674744, "Y": 0.5591302514076233}, {"X": 0.7030095458030701, "Y": 0.5591254830360413}]}, "Id": "f27531f4-6914-48c6-bd08-b49035abe364"}, {"BlockType": "WORD", "Confidence": 22.059551239013672, "Text": "0661", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04499681293964386, "Height": 0.016497431322932243, "Left": 0.7347134947776794, "Top": 0.5436173677444458}, "Polygon": [{"X": 0.7347182035446167, "Y": 0.5436173677444458}, {"X": 0.7797103524208069, "Y": 0.5436268448829651}, {"X": 0.7797059416770935, "Y": 0.5601148009300232}, {"X": 0.7347134947776794, "Y": 0.5601053237915039}]}, "Id": "3e099166-5906-4ce0-868c-926d291d4e90"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "DRIVER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06685557216405869, "Height": 0.013200006447732449, "Left": 0.16791431605815887, "Top": 0.5685729384422302}, "Polygon": [{"X": 0.1679212599992752, "Y": 0.5685729384422302}, {"X": 0.23476989567279816, "Y": 0.5685869455337524}, {"X": 0.23476333916187286, "Y": 0.5817729234695435}, {"X": 0.16791431605815887, "Y": 0.581758975982666}]}, "Id": "05abef53-8208-4648-b230-614bd0a22c59"}, {"BlockType": "WORD", "Confidence": 99.69168090820312, "Text": "SIGNATURE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11224745213985443, "Height": 0.014481700025498867, "Left": 0.17075137794017792, "Top": 0.5916962027549744}, "Polygon": [{"X": 0.17075896263122559, "Y": 0.5916962027549744}, {"X": 0.28299883008003235, "Y": 0.5917195677757263}, {"X": 0.2829919457435608, "Y": 0.6061778664588928}, {"X": 0.17075137794017792, "Y": 0.6061545014381409}]}, "Id": "f84283f0-7eeb-4313-9403-2ae9be9d4eb7"}, {"BlockType": "WORD", "Confidence": 40.36272430419922, "Text": "INSOLND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07997429370880127, "Height": 0.017756110057234764, "Left": 0.6445866227149963, "Top": 0.6210573315620422}, "Polygon": [{"X": 0.6445923447608948, "Y": 0.6210573315620422}, {"X": 0.7245609164237976, "Y": 0.6210739016532898}, {"X": 0.7245557904243469, "Y": 0.6388134360313416}, {"X": 0.6445866227149963, "Y": 0.6387969255447388}]}, "Id": "48afb48d-7184-4445-8129-b6309cc8a6f9"}, {"BlockType": "WORD", "Confidence": 25.069555282592773, "Text": "32490", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057881079614162445, "Height": 0.017446625977754593, "Left": 0.7575283646583557, "Top": 0.6223767995834351}, "Polygon": [{"X": 0.7575331330299377, "Y": 0.6223767995834351}, {"X": 0.8154094219207764, "Y": 0.6223887801170349}, {"X": 0.8154050707817078, "Y": 0.6398234367370605}, {"X": 0.7575283646583557, "Y": 0.6398114562034607}]}, "Id": "163c394b-3474-4b15-8f0c-388f60396f79"}, {"BlockType": "WORD", "Confidence": 30.740394592285156, "Text": "15", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02341609075665474, "Height": 0.016379520297050476, "Left": 0.8242581486701965, "Top": 0.6240640878677368}, "Polygon": [{"X": 0.8242621421813965, "Y": 0.6240640878677368}, {"X": 0.8476741909980774, "Y": 0.6240689158439636}, {"X": 0.8476703763008118, "Y": 0.6404436230659485}, {"X": 0.8242581486701965, "Y": 0.6404387950897217}]}, "Id": "4bf1e189-a732-45bd-9b21-7dd4f8dbdb9b"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "WEIGHER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09482968598604202, "Height": 0.01397744845598936, "Left": 0.16569936275482178, "Top": 0.6442453265190125}, "Polygon": [{"X": 0.16570672392845154, "Y": 0.6442453265190125}, {"X": 0.2605290412902832, "Y": 0.6442648768424988}, {"X": 0.2605222761631012, "Y": 0.6582227945327759}, {"X": 0.16569936275482178, "Y": 0.6582033038139343}]}, "Id": "63cc7e53-1425-49b4-82e5-5f783f16fc65"}, {"BlockType": "WORD", "Confidence": 84.5360336303711, "Text": "12:44", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055771127343177795, "Height": 0.017212191596627235, "Left": 0.5562214255332947, "Top": 0.6670264005661011}, "Polygon": [{"X": 0.5562276244163513, "Y": 0.6670264005661011}, {"X": 0.6119925379753113, "Y": 0.667037844657898}, {"X": 0.6119867563247681, "Y": 0.684238612651825}, {"X": 0.5562214255332947, "Y": 0.6842271685600281}]}, "Id": "4e3bafa4-eb37-4baa-9554-527ddcc76405"}, {"BlockType": "WORD", "Confidence": 98.08374786376953, "Text": "3152", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0459548644721508, "Height": 0.016446229070425034, "Left": 0.5659252405166626, "Top": 0.6871525049209595}, "Polygon": [{"X": 0.5659310817718506, "Y": 0.6871525049209595}, {"X": 0.6118801236152649, "Y": 0.6871618628501892}, {"X": 0.6118745803833008, "Y": 0.70359867811203}, {"X": 0.5659252405166626, "Y": 0.7035893797874451}]}, "Id": "d79e90a9-7135-4f0a-8638-64499f59534e"}, {"BlockType": "WORD", "Confidence": 58.86878204345703, "Text": "<PERSON><PERSON><PERSON>", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.17351943254470825, "Height": 0.055048540234565735, "Left": 0.24571429193019867, "Top": 0.7298856973648071}, "Polygon": [{"X": 0.24574139714241028, "Y": 0.7298856973648071}, {"X": 0.4192337095737457, "Y": 0.7299208045005798}, {"X": 0.4192107021808624, "Y": 0.7849342823028564}, {"X": 0.24571429193019867, "Y": 0.7848995923995972}]}, "Id": "7d07ea88-5777-4bb5-a185-f2615b574805"}, {"BlockType": "WORD", "Confidence": 23.612085342407227, "Text": "He<PERSON>r", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.20493084192276, "Height": 0.06063946336507797, "Left": 0.43165692687034607, "Top": 0.7333122491836548}, "Polygon": [{"X": 0.43168196082115173, "Y": 0.7333122491836548}, {"X": 0.6365877389907837, "Y": 0.7333536744117737}, {"X": 0.636568009853363, "Y": 0.793951690196991}, {"X": 0.43165692687034607, "Y": 0.7939108610153198}]}, "Id": "0633d51e-762c-4df5-a8a5-025231142788"}, {"BlockType": "WORD", "Confidence": 90.63078308105469, "Text": "14:57", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055600397288799286, "Height": 0.01779528707265854, "Left": 0.64554762840271, "Top": 0.7462238669395447}, "Polygon": [{"X": 0.6455533504486084, "Y": 0.7462238669395447}, {"X": 0.7011480331420898, "Y": 0.7462350726127625}, {"X": 0.7011427283287048, "Y": 0.7640191316604614}, {"X": 0.64554762840271, "Y": 0.7640079855918884}]}, "Id": "1b49c517-470f-426a-8c45-e722ebe487f6"}, {"BlockType": "WORD", "Confidence": 83.47676086425781, "Text": "11-01-22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08937840908765793, "Height": 0.018076036125421524, "Left": 0.7448363900184631, "Top": 0.7487182021141052}, "Polygon": [{"X": 0.744841456413269, "Y": 0.7487182021141052}, {"X": 0.8342148065567017, "Y": 0.7487362027168274}, {"X": 0.8342104554176331, "Y": 0.7667942643165588}, {"X": 0.7448363900184631, "Y": 0.7667763233184814}]}, "Id": "f3715ad4-2348-4812-823d-fad53fd24003"}, {"BlockType": "WORD", "Confidence": 92.61419677734375, "Text": "11/2/2020", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.14967866241931915, "Height": 0.03506859764456749, "Left": 0.4705485999584198, "Top": 0.7740284204483032}, "Polygon": [{"X": 0.47056248784065247, "Y": 0.7740284204483032}, {"X": 0.6202272772789001, "Y": 0.7740584015846252}, {"X": 0.6202155947685242, "Y": 0.8090969920158386}, {"X": 0.4705485999584198, "Y": 0.8090672492980957}]}, "Id": "db0d92dd-a23f-49c4-bf30-dcf3b6e1ef82"}, {"BlockType": "WORD", "Confidence": 72.45077514648438, "Text": "58260", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05887312442064285, "Height": 0.017965994775295258, "Left": 0.66522616147995, "Top": 0.8069742918014526}, "Polygon": [{"X": 0.6652317643165588, "Y": 0.8069742918014526}, {"X": 0.7240992784500122, "Y": 0.8069860339164734}, {"X": 0.7240940928459167, "Y": 0.8249403238296509}, {"X": 0.66522616147995, "Y": 0.8249286413192749}]}, "Id": "0b0e1ab0-3bff-44b9-8112-fd908c30217e"}, {"BlockType": "WORD", "Confidence": 13.596240997314453, "Text": "In", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021688375622034073, "Height": 0.016423355787992477, "Left": 0.7343248724937439, "Top": 0.8077173829078674}, "Polygon": [{"X": 0.7343295216560364, "Y": 0.8077173829078674}, {"X": 0.7560132741928101, "Y": 0.807721734046936}, {"X": 0.7560087442398071, "Y": 0.824140727519989}, {"X": 0.7343248724937439, "Y": 0.8241364359855652}]}, "Id": "6ce6e978-92fa-48f3-a15b-a59e80760bc3"}, {"BlockType": "WORD", "Confidence": 60.500240325927734, "Text": "GROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055170997977256775, "Height": 0.01688331738114357, "Left": 0.7659662365913391, "Top": 0.8076465725898743}, "Polygon": [{"X": 0.765970766544342, "Y": 0.8076465725898743}, {"X": 0.8211371898651123, "Y": 0.8076575398445129}, {"X": 0.821133017539978, "Y": 0.8245298862457275}, {"X": 0.7659662365913391, "Y": 0.8245189785957336}]}, "Id": "e68f9151-06d6-4200-87cf-99725eefd292"}, {"BlockType": "WORD", "Confidence": 35.57916259765625, "Text": "PA", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.3912660479545593, "Height": 0.10972226411104202, "Left": 0.15533116459846497, "Top": 0.8131336569786072}, "Polygon": [{"X": 0.15538941323757172, "Y": 0.8131336569786072}, {"X": 0.5465972423553467, "Y": 0.8132113218307495}, {"X": 0.5465573072433472, "Y": 0.9228559136390686}, {"X": 0.15533116459846497, "Y": 0.9227801561355591}]}, "Id": "b260c5cc-f587-410a-b907-44d602f0b936"}, {"BlockType": "WORD", "Confidence": 42.23014831542969, "Text": "400", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034365251660346985, "Height": 0.016346897929906845, "Left": 0.6903668642044067, "Top": 0.8265507817268372}, "Polygon": [{"X": 0.6903718113899231, "Y": 0.8265507817268372}, {"X": 0.7247321605682373, "Y": 0.8265575766563416}, {"X": 0.7247274518013, "Y": 0.8428977131843567}, {"X": 0.6903668642044067, "Y": 0.8428909182548523}]}, "Id": "0e11d861-d688-4f5d-b150-99e5b8104fdd"}, {"BlockType": "WORD", "Confidence": 27.813297271728516, "Text": "5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021958019584417343, "Height": 0.015524042770266533, "Left": 0.7364304065704346, "Top": 0.8285086750984192}, "Polygon": [{"X": 0.7364304065704346, "Y": 0.8440284132957458}, {"X": 0.736434817314148, "Y": 0.8285086750984192}, {"X": 0.7583884596824646, "Y": 0.8285130262374878}, {"X": 0.7583842277526855, "Y": 0.8440327048301697}]}, "Id": "a515967c-4f9f-4cbe-a64b-98aba7d590d5"}, {"BlockType": "WORD", "Confidence": 72.61260223388672, "Text": "TARE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04690805822610855, "Height": 0.01722428947687149, "Left": 0.766647219657898, "Top": 0.8278085589408875}, "Polygon": [{"X": 0.7666518688201904, "Y": 0.8278085589408875}, {"X": 0.8135553002357483, "Y": 0.8278177976608276}, {"X": 0.8135509490966797, "Y": 0.845032811164856}, {"X": 0.766647219657898, "Y": 0.8450235724449158}]}, "Id": "cefb38a6-2e97-4618-a9fe-e8fecdb7f878"}, {"BlockType": "WORD", "Confidence": 26.75522232055664, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022934401407837868, "Height": 0.015477005392313004, "Left": 0.7357911467552185, "Top": 0.84880131483078}, "Polygon": [{"X": 0.7357955574989319, "Y": 0.84880131483078}, {"X": 0.7587255835533142, "Y": 0.848805844783783}, {"X": 0.7587213516235352, "Y": 0.8642783164978027}, {"X": 0.7357911467552185, "Y": 0.8642738461494446}]}, "Id": "597a7c5f-6e0c-488d-82fe-5cadf30ee96f"}, {"BlockType": "WORD", "Confidence": 96.78870391845703, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035266563296318054, "Height": 0.01592062972486019, "Left": 0.7671415209770203, "Top": 0.8482168316841125}, "Polygon": [{"X": 0.7671458125114441, "Y": 0.8482168316841125}, {"X": 0.8024080991744995, "Y": 0.8482237458229065}, {"X": 0.8024040460586548, "Y": 0.8641374707221985}, {"X": 0.7671415209770203, "Y": 0.8641305565834045}]}, "Id": "15ac7840-b2e1-4bd2-8dec-830b04a3507a"}, {"BlockType": "WORD", "Confidence": 21.733198165893555, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01951032504439354, "Height": 0.01703607477247715, "Left": 0.5947649478912354, "Top": 0.8667954206466675}, "Polygon": [{"X": 0.6142752766609192, "Y": 0.8667992353439331}, {"X": 0.6142695546150208, "Y": 0.8838315010070801}, {"X": 0.5947649478912354, "Y": 0.8838276863098145}, {"X": 0.5947707891464233, "Y": 0.8667954206466675}]}, "Id": "5800b8be-84f7-4009-a20f-e296d74d1dc6"}, {"BlockType": "WORD", "Confidence": 89.41107177734375, "Text": "11-2-22", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.1454796940088272, "Height": 0.024066345766186714, "Left": 0.3973536789417267, "Top": 0.9159774780273438}, "Polygon": [{"X": 0.39736396074295044, "Y": 0.9159774780273438}, {"X": 0.5428333878517151, "Y": 0.9160057306289673}, {"X": 0.5428245663642883, "Y": 0.9400438666343689}, {"X": 0.3973536789417267, "Y": 0.9400157928466797}]}, "Id": "96a9aea8-f0ac-4899-aa06-f6941bf64328"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "5873de03-99c7-4d38-8ee2-f5ae12ef4f3a", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "5873de03-99c7-4d38-8ee2-f5ae12ef4f3a", "content-type": "application/x-amz-json-1.1", "content-length": "57539", "date": "Thu, 18 Sep 2025 07:31:24 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "b8580da8-c4cb-40f0-8eff-3f7e985af7b1", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Thu, 18 Sep 2025 07:31:26 GMT", "content-type": "application/json", "content-length": "587", "connection": "keep-alive", "x-amzn-requestid": "b8580da8-c4cb-40f0-8eff-3f7e985af7b1"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page, with header \"ELECTRONIC SCALE TICKET\". This matches the scale_ticket type. No other document type. So output for page1: doc_type: \"scale_ticket\". Use tool."}}}, {"toolUse": {"toolUseId": "tooluse_s8v5fDmPQwq0ZC41C-q5yg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "scale_ticket"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2220, "outputTokens": 83, "totalTokens": 2303}, "metrics": {"latencyMs": 597}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/8201ca49_6_scale_ticket_4.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. You strictly these rules and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If none of the specialized patterns match, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strickly check for contination of previous page by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc. or page 2 of 3, etc.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for services rendered (generic carrier billing).\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: \"Scale Ticket\", Gross Weight, Tare, Net Weight, Weighed At\n        Key fields/structure: gross/tare/net values, scale location, date/time, truck ID.\n\n        log — Log\n        Definition: Activity record (driver log, tracking log) for operational/regulatory use.\n        Keywords indication: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp\n        Key fields/structure: timestamped activity rows, driver/vehicle IDs, mileage or status entries.\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        nmfc_cert — NMFC Certificate\n        Definition: Document showing National Motor Freight Classification codes/class assignments.\n        Keywords indication: NMFC, National Motor Freight Classification, Class\n        Key fields/structure: NMFC codes, class #, commodity description.\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        coa — Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Definition: Certificate/Report issued by authority confirming composition/quality of goods with acceptance criteria plus issuer and issue date/signature\n        Keywords indication: Certificate of Analysis, COA, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Key fields/structure: analysis/test results, batch/lot numbers, lab accreditation.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        lumper_receipt — Lumper Receipt\n        Definition: Receipt for lumper services (loading/unloading labor).\n        Keywords indication: Lumper, Lumper Receipt, Lumper Charge\n        Key fields/structure: labor charge, lumper name/signature, location/date.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        po_confirmation — Purchase Order Confirmation\n        Definition: Seller's acceptance/confirmation of a buyer's PO.\n        Keywords indication: Purchase Order Confirmation, PO Confirmation, Acknowledgement\n        Key fields/structure: PO reference, acceptance terms, confirmed ship/dates.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nELECTRONIC SCALE TICKET\nEIL\nNC\nDATE:\nMAILING\nADDRESS\nDLA DISPOSITION SERVICES - COLUMBUS\n3990 E. BROAD ST, BLDG 30\nCOLUMBUS, OH 43213\nPH:\n614-692-2055\nSHIP TO\nADDRESS\nDLA DISPOSITION SERVICES - COLUMBUS\n320 N. JAMES RD, BLDG 30\nCOLUMBUS, OH 43213\nREMARKS:\n04\n11-01-22\nLOOP to 0661\nDRIVER\nSIGNATURE:\nINSOLND 32490 15\nWEIGHER:\n12:44\n3152\n<PERSON><PERSON><PERSON>r\n14:57\n11-01-22\n11/2/2020\n58260 In GROSS\nPA\n400\n5\nTARE\nin NET\n&\n11-2-22\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "nmfc_cert", "other", "coa", "lumper_receipt", "so_confirmation", "po_confirmation"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}